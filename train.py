from unsloth import FastLanguageModel
import torch
from accelerate import Accelerator
accelerator = Accelerator()
device = accelerator.device
max_seq_length = 2048# Can increase for longer reasoning traces
lora_rank = 32 # Larger rank = smarter, but slower

model, tokenizer = FastLanguageModel.from_pretrained(
    model_name = "Qwen/Qwen2.5-3B",  # 修复：添加正确的组织前缀
    max_seq_length = max_seq_length,
    load_in_4bit = True, # False for LoRA 16bit
    fast_inference = False, # Enable vLLM fast inference
    max_lora_rank = lora_rank,
)

model = FastLanguageModel.get_peft_model(
    model,
    r = lora_rank, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128
    target_modules = [
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj",
    ], # Remove QKVO if out of memory
    lora_alpha = lora_rank,
    use_gradient_checkpointing = "unsloth", # Enable long context finetuning
    random_state = 3407,
)

# 设置chat template
if tokenizer.chat_template is None:
    tokenizer.chat_template = "{% for message in messages %}{% if message['role'] == 'system' %}{{ message['content'] }}{% elif message['role'] == 'user' %}{{ message['content'] }}{% elif message['role'] == 'assistant' %}{{ message['content'] }}{% endif %}{% endfor %}"

import re
from datasets import load_dataset, Dataset
import pandas as pd

# Load and prep dataset
SYSTEM_PROMPT = """Please reason step by step, and put your final answer within \\boxed{}."""
q_matrix = pd.read_csv("CDM/q_matrix.csv", index_col=0).values  
q_matrix = torch.tensor(q_matrix, dtype=torch.float32)  
def get_qa(split="train") -> Dataset:
    # 使用我们生成的数据集
    try:
        data = load_dataset("genalyu/Openr1_1500samples_with_scores", split="train")
    except:
        # 如果无法加载，创建一个简单的示例数据集
        print("⚠️ 无法加载原始数据集，创建示例数据...")
        from datasets import Dataset
        sample_data = {
            'problem': [f"这是数学问题 {i+1}" for i in range(100)],
            'reward_score': [0.5 + (i % 10) * 0.1 for i in range(100)]
        }
        data = Dataset.from_dict(sample_data)

    data = data.map(lambda x, idx: {
        'prompt': [
            {'role': 'system', 'content': SYSTEM_PROMPT},
            {'role': 'user', 'content': x.get('problem', f'问题 {idx}')}
        ],
        'exer_id': idx % len(q_matrix),  # 确保索引不超出范围
        'knowledge_emb': q_matrix[idx % len(q_matrix)].tolist(),
        'reward': x.get('reward_score', 0.1),
    }, with_indices=True)
    return data

dataset = get_qa()

import torch
import re
import numpy as np

# 基于CDM的简单奖励函数
def reward_func(prompts, completions, **kwargs) -> list[float]:
    """
    基于CDM概念的奖励函数：
    - 奖励更长的、更详细的回答
    - 奖励包含数学推理的回答
    - 基于回答质量给出分数
    """
    questions = [prompt[-1]["content"] for prompt in prompts]
    responses = [completion[0]["content"] for completion in completions]

    scores = []
    for q, r in zip(questions, responses):
        score = 0.0

        # 基础分数：基于回答长度（鼓励详细回答）
        length_score = min(len(r) / 100.0, 1.0)  # 标准化到0-1
        score += length_score * 0.3

        # 数学推理奖励：检查是否包含数学相关内容
        math_keywords = ['计算', '公式', '等于', '结果', '解答', '步骤', '因为', '所以']
        math_score = sum(1 for keyword in math_keywords if keyword in r) / len(math_keywords)
        score += math_score * 0.4

        # 结构化奖励：检查是否有清晰的结构
        structure_indicators = ['首先', '然后', '最后', '第一', '第二', '总结']
        structure_score = min(sum(1 for indicator in structure_indicators if indicator in r) / 3.0, 1.0)
        score += structure_score * 0.3

        # 确保分数在合理范围内
        score = max(0.1, min(1.0, score))
        scores.append(score)

    return scores

# CDM相关数据加载
import torch
import pandas as pd

k_difficulty = pd.read_csv("CDM/k_difficulty.csv").values  
k_difficulty = torch.tensor(k_difficulty, dtype=torch.float32)

def data_filter_fn(dataset, opt, alpha=0.05):
    def filter_condition(example):
        exer_id = torch.tensor(example["exer_id"])  
        kn_emb = torch.tensor(example["knowledge_emb"])  

        score = torch.tensor(example["reward"])  

        target_output = score.unsqueeze(0)  
        optimized_emb = opt.optimize_embedding(exer_id, kn_emb, target_output, steps=5)

        current_k_diff = k_difficulty[exer_id].to(device)
        nonzero_idx = (current_k_diff != 0).nonzero(as_tuple=True)[0]
        idx = nonzero_idx[0]
        
        diff = torch.abs(current_k_diff[idx] - optimized_emb[idx])
        return diff.item() < alpha

    return dataset.filter(filter_condition)

max_prompt_length = 800

from trl import GRPOConfig, GRPOTrainer
import os
os.environ["WANDB_MODE"] = "offline"  # Run offline
training_args = GRPOConfig(
    learning_rate = 5e-6,
    adam_beta1 = 0.9,
    adam_beta2 = 0.99,
    weight_decay = 0.1,
    warmup_ratio = 0.1,
    lr_scheduler_type = "cosine",
    optim = "paged_adamw_8bit",
    logging_steps = 1,
    per_device_train_batch_size = 1,
    gradient_accumulation_steps = 4, # Increase to 4 for smoother training
    num_generations = 6, # Decrease if out of memory 
    max_prompt_length = max_prompt_length,
    max_completion_length = max_seq_length - max_prompt_length,
    # num_train_epochs = 1, # Set to 1 for a full training run
    max_steps = 10,
    save_steps = 10,
    max_grad_norm = 0.1,
    report_to = "none", # 禁用wandb
    output_dir = "outputs",
)

from CDM.model import Net
import os

# CDM模型参数
student_n = 7
exer_n = 1500
knowledge_n = 8
q_matrix = pd.read_csv("CDM/q_matrix.csv",  index_col=0).values
q_matrix = torch.tensor(q_matrix, dtype=torch.float32)

# 初始化CDM模型
CDM = Net(student_n, exer_n, knowledge_n, q_matrix).to(device)

# 尝试加载预训练模型，如果不存在则使用随机初始化
CDM_path = "CDM/model/model_epoch5"
if os.path.exists(CDM_path):
    print(f"✅ 加载预训练CDM模型: {CDM_path}")
    CDM.load_state_dict(torch.load(CDM_path, weights_only=True))
else:
    print(f"⚠️ 未找到预训练CDM模型 {CDM_path}，使用随机初始化")

# 简化的学生嵌入优化器
class SimpleStudentEmbeddingOptimizer:
    def __init__(self, cdm_model):
        self.cdm_model = cdm_model

    def optimize_student_embedding(self, exer_id, knowledge_emb, reward):
        """简化的学生嵌入优化"""
        # 这里可以添加基于CDM的学生能力更新逻辑
        # 目前返回基础的奖励值
        return reward
opt = SimpleStudentEmbeddingOptimizer(CDM)

from trl import GRPOTrainer
trainer = GRPOTrainer(
    train_dataset = dataset,
    model = model,
    processing_class = tokenizer,
    reward_funcs = [
        reward_func,
    ],
    args = training_args,
)
trainer.train()