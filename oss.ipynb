{"cells": [{"cell_type": "code", "execution_count": 1, "id": "27d42ffd", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": null, "id": "8ffb5cc2", "metadata": {}, "outputs": [], "source": ["os.name"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}