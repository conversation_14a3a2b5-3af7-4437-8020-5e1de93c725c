from unsloth import FastLanguageModel # 高效加载模型的库
import torch # PyTorch 深度学习框架

# 模型配置参数
max_seq_length = 1024 # 最大输入序列长度（影响显存占用）
lora_rank = 32 # LoRA的秩，越大的值，模型能力越强，但也会越慢

# 加载基础模型
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name = "Qwen/Qwen2-1.5B-Instruct", 
    max_seq_length = max_seq_length,
    load_in_4bit = True, # 为True则使用4bit量化(显存优化)，为False则使用16bit
    fast_inference = True, # 启用 vLLM 加速推理
    max_lora_rank = lora_rank, # LoRA 最大秩限制
    gpu_memory_utilization = 0.6, # GPU 显存利用率（如果OOM可调低）
)

text = tokenizer.apply_chat_template([ 
    {"role" : "user", "content" : "Calculate pi."},
], 
tokenize = False, # 表示不对输入进行分词
add_generation_prompt = True)

# 设置采样参数
from vllm import SamplingParams
sampling_params = SamplingParams( 
    temperature = 0.8,
    top_p = 0.95,
    max_tokens = 1024, # 生成文本的最大长度
)

# 生成文本
output = model.fast_generate( 
    [text],
    sampling_params = sampling_params,
    lora_request = None, # 表示不使用 LoRA（低秩适应）请求
)[0].outputs[0].text

print(output)

import torch.distributed as dist

# 在print(output)之后添加
if dist.is_initialized():
    dist.destroy_process_group()