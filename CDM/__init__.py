# CDM (Cognitive Diagnosis Model) Package
"""
CDM4GRPO - Cognitive Diagnosis Model with Group Relative Policy Optimization

This package contains the implementation of cognitive diagnosis models
combined with GRPO training methodology.
"""

__version__ = "1.0.0"
__author__ = "CDM4GRPO Team"

# Import main modules (only import what's needed for the package to work)
try:
    from . import model
    from . import data_loader
    # Don't import train module by default to avoid execution
    # from . import train
except ImportError as e:
    print(f"Warning: Could not import some CDM modules: {e}")

# Make train module available for explicit import
def get_train_module():
    """Get the train module when needed"""
    try:
        from . import train
        return train
    except ImportError as e:
        print(f"Could not import train module: {e}")
        return None
