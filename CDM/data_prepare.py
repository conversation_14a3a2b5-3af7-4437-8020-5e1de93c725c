import pandas as pd
from datasets import load_dataset

# 安全加载数据集，跳过不存在的数据集
datasets = {}
dataset_names = [
    "genalyu/gemini-2.0-flash-1500samples-with-scores",
    "genalyu/sillyECNU-lite-1500samples-with-scores",
    "genalyu/ECNU-lite-1500samples-with-scores",
    "genalyu/ECNU-plus-1500samples-with-scores",
    "genalyu/gemini-1.5-flash-8B-1500samples-with-scores",
    "genalyu/Openr1_1500samples_with_scores",
    "genalyu/gemini-1.5-flash-1500samples-with-scores"
]

for i, name in enumerate(dataset_names, 1):
    try:
        print(f"正在加载数据集 {name}...")
        datasets[f'd{i}'] = load_dataset(name, split="train")
        print(f"✅ 成功加载 d{i}")
    except Exception as e:
        print(f"❌ 跳过数据集 d{i}: {e}")
        continue

# 转换为DataFrame
dataframes = {}
for key, dataset in datasets.items():
    dataframes[key] = pd.DataFrame(dataset)

# 处理成功加载的数据集
for key, df in dataframes.items():
    # 添加用户ID
    df["user_id"] = key

    # 计算分数
    if "reward_score" in df.columns:
        df["score"] = ((df["reward_score"].astype(float)) + 2) / 9
    else:
        # 如果没有reward_score列，使用默认分数
        df["score"] = 0.5

    # 添加项目ID
    df["item_id"] = df.index

# 合并所有交互数据
interaction_dfs = []
for key, df in dataframes.items():
    if len(df) > 0:
        interaction_dfs.append(df[["user_id", "item_id", "score"]])

if interaction_dfs:
    interactions = pd.concat(interaction_dfs, ignore_index=True)
    interactions.to_csv("interaction.csv", index=False)
    print(f"✅ 生成interaction.csv，包含 {len(interactions)} 条记录")
else:
    print("❌ 没有可用的数据集来生成interaction.csv")

# 生成Q矩阵
def parse_knowledge_tags(tag_str):
    return tag_str.split('+')

def one_hot(tags, tag_list):
    tag_set = set(tags.split('+'))
    return [1 if tag in tag_set else 0 for tag in tag_list]

# 寻找包含problem_type列的数据集
problem_type_df = None
for key, df in dataframes.items():
    if "problem_type" in df.columns:
        problem_type_df = df
        print(f"✅ 使用 {key} 数据集生成Q矩阵")
        break

if problem_type_df is not None:
    # 提取所有标签
    all_tags = sorted(set(
        tag for row in problem_type_df["problem_type"].tolist()
        for tag in parse_knowledge_tags(row)
    ))

    # 构建Q矩阵
    q_matrix = pd.DataFrame(
        [one_hot(tags, all_tags) for tags in problem_type_df["problem_type"]],
        columns=all_tags
    )
    q_matrix.insert(0, "item_id", problem_type_df["item_id"])
    q_matrix.to_csv("q_matrix.csv", index=False)
    print(f"✅ 生成q_matrix.csv，包含 {len(all_tags)} 个知识点")
else:
    # 如果没有problem_type列，创建一个简单的Q矩阵
    print("⚠️ 没有找到problem_type列，创建简单的Q矩阵")
    # 使用第一个可用的数据集
    if dataframes:
        first_df = list(dataframes.values())[0]
        n_items = len(first_df)
        n_knowledge = 5  # 假设5个知识点

        # 创建随机的Q矩阵
        import numpy as np
        np.random.seed(42)
        q_matrix_data = np.random.randint(0, 2, size=(n_items, n_knowledge))

        q_matrix = pd.DataFrame(
            q_matrix_data,
            columns=[f"knowledge_{i}" for i in range(n_knowledge)]
        )
        q_matrix.insert(0, "item_id", range(n_items))
        q_matrix.to_csv("q_matrix.csv", index=False)
        print(f"✅ 生成简单q_matrix.csv，包含 {n_knowledge} 个知识点")
    else:
        print("❌ 无法生成Q矩阵，没有可用数据")