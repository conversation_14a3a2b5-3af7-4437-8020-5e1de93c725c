from unsloth import FastLanguageModel
import torch

max_seq_length = 1024
lora_rank = 32
model,tokenizer=FastLanguageModel.from_pretrained(
    model_name='Qwen/Qwen2-1.5B-Instruct',
    max_seq_length=max_seq_length,
    load_in_4bit=True,
    fast_inference=True,
    max_lora_rank=lora_rank,
    gpu_memory_utilization=0.6,
)

text=tokenizer.applu_chat_template([
    {"role":"user","content":"Calculate pi."},
],
    tokenize=False,
    add_generation_prompt=True ,                                  
                                   )


from vllm import SamplingParams

sampling_params=SamplingParams(
    temperature=0.8,
    top_p=0.95,
    max_tokens=1024
)

output=model.fast_generate(
    [text],
    sampling_params=sampling_params,
    lora_request=None,
)[0].outputs[0].text

print(output)


import re
from datasets import load_dataset,Dataset

STSTEM_PROMPT="""
Respond in the following format:
<reasoning>
...
</reasoning>
<answer>
...
</answer>"""

def extract_hash_answer(tetxt):
    if "####" not in text:
        return None
    return text.split("####")[1].strip()

def get_gsm9k_question(split="train"):
    data=load_dataset('openai/gsm8k','main')[split]
    data=data.map(lambda x:{
        'prompt':[
            {'role':'system','content':'SYSTEM_PROMPT'},
            {'role':'user','content':x['question']}
        ],
        'answer':extract_hash_answer(x['answer'])
        
    }    
    )
    return data

model=FastLanguageModel.get_peft_model(
    model,
    r=lora_rank,
    target_models=[
                "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj",
    ],
    lora_alpha=lora_rank,
    use_gradient_checkpointing="unsloth",
    random_state=3407,
)

    
    
max_prompt_length=256

from trl import GRPOConfig,GRPOTrainer
training_arfs=GRPOConfig(
    learining_rate-5e-6,
    adam_beta1=0.9,
    adam_beta2=0.99,
    weight_decay=0.1,
    warmup_ratio=0.1,
    lr_scheduler_type
    
)

















